from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
from .models import (
    MaterialPadrao, Material, Mola, MovimentacaoEstoque, MovimentacaoMaterial,
    PedidoVenda, ItemPedido, PrevisaoDemanda, AnaliseObsolescencia,
    PlanejamentoProducao, ItemPlanejamento
)
from .utils import ordenar_materiais, extrair_valor_numerico_diametro


class MaterialPadraoForm(forms.ModelForm):
    class Meta:
        model = MaterialPadrao
        fields = ['nome', 'diametro']
        widgets = {
            'nome': forms.TextInput(attrs={'class': 'form-control'}),
            'diametro': forms.TextInput(attrs={'class': 'form-control'}),
        }


class MaterialForm(forms.ModelForm):
    class Meta:
        model = Material
        fields = ['material_padrao', 'nome', 'descricao', 'diametro', 'quantidade_estoque', 'estoque_minimo',
                 'fornecedor', 'data', 'nota_fiscal', 'ativo']
        widgets = {
            'material_padrao': forms.Select(attrs={'class': 'form-select'}),
            'descricao': forms.Textarea(attrs={'rows': 3}),
            'data': forms.DateInput(attrs={'type': 'date'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Ordenar os materiais padrão por nome
        if 'material_padrao' in self.fields:
            self.fields['material_padrao'].queryset = MaterialPadrao.objects.all().order_by('nome')


class PesoUnitarioWidget(forms.TextInput):
    """Widget personalizado para o campo peso_unitario"""
    def get_context(self, name, value, attrs):
        """Sobrescreve o método para garantir que o valor seja formatado corretamente"""
        context = super().get_context(name, value, attrs)
        # Garantir que o valor seja formatado corretamente
        if value is not None:
            # Converter para string e garantir que seja exibido com 4 casas decimais
            from decimal import Decimal
            try:
                # Formatar o valor para garantir 4 casas decimais
                value_decimal = Decimal(str(value)).quantize(Decimal('0.0001'))
                # Definir o valor formatado no widget (usando vírgula para formato brasileiro)
                context['widget']['value'] = str(value_decimal).replace('.', ',')
            except:
                # Se houver erro na conversão, usar o valor original
                pass
        return context

    def value_from_datadict(self, data, files, name):
        """Sobrescreve o método para converter corretamente valores com vírgula para ponto"""
        value = super().value_from_datadict(data, files, name)
        if value:
            # Substituir vírgula por ponto para processamento correto
            value = value.replace(',', '.')
        return value


class MolaForm(forms.ModelForm):
    # Definir o campo peso_unitario explicitamente para usar o widget personalizado
    peso_unitario = forms.DecimalField(
        max_digits=10,
        decimal_places=4,
        required=False,
        widget=PesoUnitarioWidget(attrs={'class': 'form-control', 'placeholder': '0,0000'}),
        help_text="Peso unitário da mola em gramas (medido manualmente)"
    )

    class Meta:
        model = Mola
        fields = ['codigo', 'nome_mola', 'cliente', 'descricao', 'material_padrao', 'material', 'diametro',
                  'volumes', 'quantidade_por_volume', 'quantidade_estoque',
                  'estoque_minimo', 'ordem_fabricacao', 'data', 'peso_unitario']
        widgets = {
            'descricao': forms.Textarea(attrs={'rows': 3}),
            'material_padrao': forms.Select(attrs={'class': 'form-select'}),
            'material': forms.Select(attrs={'class': 'form-select'}),
            'data': forms.DateInput(attrs={'type': 'date'}),
        }

    def __init__(self, *args, **kwargs):
        # Verificar se há uma instância com valor de peso_unitario
        instance = kwargs.get('instance')
        if instance and instance.pk and instance.peso_unitario is not None:
            # Se houver dados POST, criar uma cópia para modificar
            if 'data' in kwargs and kwargs['data']:
                data = kwargs['data'].copy()
                # Adicionar o valor do peso_unitario aos dados
                prefix = kwargs.get('prefix', '')
                field_name = f"{prefix}-peso_unitario" if prefix else 'peso_unitario'
                if field_name not in data or not data.get(field_name):
                    data[field_name] = str(instance.peso_unitario)
                kwargs['data'] = data

            # Definir o valor inicial
            initial = kwargs.get('initial', {})
            initial['peso_unitario'] = instance.peso_unitario
            kwargs['initial'] = initial

        super().__init__(*args, **kwargs)

        # Garantir que o valor do peso_unitario seja inicializado corretamente para edição
        if self.instance and self.instance.pk and self.instance.peso_unitario is not None:
            # Definir o valor inicial do campo peso_unitario
            self.initial['peso_unitario'] = self.instance.peso_unitario
            # Definir o valor diretamente no widget
            self.fields['peso_unitario'].widget.attrs['value'] = str(self.instance.peso_unitario)
            # Forçar o valor no campo
            if self.data:
                self.data = self.data.copy() if hasattr(self.data, 'copy') else self.data
                if hasattr(self.data, 'setdefault'):
                    self.data.setdefault(self.add_prefix('peso_unitario'), str(self.instance.peso_unitario))

    def clean_peso_unitario(self):
        """Garante que o valor do peso unitário seja processado corretamente"""
        peso_unitario = self.cleaned_data.get('peso_unitario')

        # Verificar se o valor veio do formulário (pode estar como string com vírgula)
        peso_unitario_raw = self.data.get(self.add_prefix('peso_unitario'))
        if peso_unitario_raw and isinstance(peso_unitario_raw, str):
            # Substituir vírgula por ponto para processamento correto
            peso_unitario_raw = peso_unitario_raw.replace(',', '.')
            try:
                from decimal import Decimal
                peso_unitario = Decimal(peso_unitario_raw)
            except:
                # Se não conseguir converter, usar o valor processado pelo Django
                pass

        # Se o valor estiver vazio mas a instância tiver um valor, usar o valor da instância
        if peso_unitario is None and self.instance and self.instance.pk and self.instance.peso_unitario is not None:
            peso_unitario = self.instance.peso_unitario

        # Garantir que o valor seja convertido para Decimal com 4 casas decimais
        if peso_unitario is not None:
            from decimal import Decimal
            # Formatar o valor para garantir 4 casas decimais
            peso_unitario = Decimal(str(peso_unitario)).quantize(Decimal('0.0001'))

        return peso_unitario


class MovimentacaoEstoqueForm(forms.ModelForm):
    class Meta:
        model = MovimentacaoEstoque
        fields = ['mola', 'tipo', 'quantidade', 'ordem_venda', 'observacao']
        widgets = {
            'observacao': forms.Textarea(attrs={'rows': 3}),
        }

    def clean(self):
        cleaned_data = super().clean()
        tipo = cleaned_data.get('tipo')
        quantidade = cleaned_data.get('quantidade')
        mola = cleaned_data.get('mola')

        if tipo and quantidade and mola and tipo == 'S':
            if quantidade > mola.quantidade_estoque:
                raise ValidationError(f"Quantidade insuficiente em estoque. Disponível: {mola.quantidade_estoque}")

        return cleaned_data


class MovimentacaoMultiplaForm(forms.Form):
    cliente = forms.ModelChoiceField(
        queryset=Mola.objects.values_list('cliente', flat=True).distinct(),
        to_field_name='cliente',
        empty_label="Selecione o cliente",
        required=True,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    tipo = forms.ChoiceField(
        choices=MovimentacaoEstoque.TIPO_CHOICES,
        required=True,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    ordem_venda = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    observacao = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'})
    )

    # Campos dinâmicos para molas e quantidades serão adicionados via JavaScript


class MovimentacaoMaterialForm(forms.ModelForm):
    class Meta:
        model = MovimentacaoMaterial
        fields = ['material', 'tipo', 'quantidade', 'ordem_compra', 'observacao']
        widgets = {
            'observacao': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Ordenar os materiais por nome e diâmetro numérico
        if 'material' in self.fields:
            # Usar QuerySet diretamente ao invés de lista para evitar erro 'list' object has no attribute 'all'
            self.fields['material'].queryset = Material.objects.filter(ativo=True).order_by('nome', 'diametro')

    def clean(self):
        cleaned_data = super().clean()
        tipo = cleaned_data.get('tipo')
        quantidade = cleaned_data.get('quantidade')
        material = cleaned_data.get('material')

        if tipo and quantidade and material and tipo == 'S':
            if quantidade > material.quantidade_estoque:
                raise ValidationError(f"Quantidade insuficiente em estoque. Disponível: {material.quantidade_estoque}")

        return cleaned_data


class PedidoVendaForm(forms.ModelForm):
    class Meta:
        model = PedidoVenda
        fields = ['numero_pedido', 'cliente', 'data_pedido', 'status', 'observacao']
        widgets = {
            'data_pedido': forms.DateInput(attrs={'type': 'date'}),
            'observacao': forms.Textarea(attrs={'rows': 3}),
        }


class ItemPedidoForm(forms.ModelForm):
    class Meta:
        model = ItemPedido
        fields = ['mola', 'quantidade', 'movimentar_estoque']
        widgets = {
            'movimentar_estoque': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        self.pedido = kwargs.pop('pedido', None)
        super().__init__(*args, **kwargs)
        self.fields['movimentar_estoque'].label = "Movimentar Estoque"
        self.fields['movimentar_estoque'].help_text = "Desmarque esta opção para registrar a venda sem deduzir do estoque"

    def clean(self):
        cleaned_data = super().clean()
        mola = cleaned_data.get('mola')
        quantidade = cleaned_data.get('quantidade')
        movimentar_estoque = cleaned_data.get('movimentar_estoque')

        if mola and quantidade and movimentar_estoque:
            if quantidade > mola.quantidade_estoque:
                raise ValidationError(f"Quantidade insuficiente em estoque. Disponível: {mola.quantidade_estoque}")

        return cleaned_data

    def save(self, commit=True):
        item = super().save(commit=False)
        if self.pedido:
            item.pedido = self.pedido
        if commit:
            item.save()
        return item


class RelatorioMolasForm(forms.Form):
    PERIODO_CHOICES = [
        ('', 'Todo o período'),
        ('semana', 'Última semana'),
        ('mes', 'Último mês'),
        ('3meses', 'Últimos 3 meses'),
        ('6meses', 'Últimos 6 meses'),
        ('ano', 'Último ano'),
        ('personalizado', 'Período personalizado'),
        ('meses_especificos', 'Meses Específicos'),
        ('comparacao', 'Comparação entre períodos'),
    ]

    # Lista de meses para seleção
    MESES_CHOICES = [
        ('1', 'Janeiro'),
        ('2', 'Fevereiro'),
        ('3', 'Março'),
        ('4', 'Abril'),
        ('5', 'Maio'),
        ('6', 'Junho'),
        ('7', 'Julho'),
        ('8', 'Agosto'),
        ('9', 'Setembro'),
        ('10', 'Outubro'),
        ('11', 'Novembro'),
        ('12', 'Dezembro'),
    ]

    # Anos disponíveis (ano atual e 2 anos anteriores)
    def get_anos_choices():
        ano_atual = timezone.now().year
        return [(str(ano), str(ano)) for ano in range(ano_atual-2, ano_atual+1)]

    periodo = forms.ChoiceField(choices=PERIODO_CHOICES, required=False, label="Período")
    data_inicial = forms.DateField(
        required=False,
        label="Data Inicial",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    data_final = forms.DateField(
        required=False,
        label="Data Final",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    subtitulo_personalizado = forms.CharField(
        max_length=100,
        required=False,
        label="Subtítulo personalizado",
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Ex: Primeiro trimestre 2023'})
    )
    meses_selecionados = forms.MultipleChoiceField(
        choices=MESES_CHOICES,
        required=False,
        label="Selecione os meses",
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-input'})
    )
    ano_selecionado = forms.ChoiceField(
        choices=get_anos_choices,
        required=False,
        label="Ano",
        initial=str(timezone.now().year),
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    # Campos para comparação entre períodos
    data_inicial_comparacao1 = forms.DateField(
        required=False,
        label="Período 1 - Data inicial",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    data_final_comparacao1 = forms.DateField(
        required=False,
        label="Período 1 - Data final",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    data_inicial_comparacao2 = forms.DateField(
        required=False,
        label="Período 2 - Data inicial",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    data_final_comparacao2 = forms.DateField(
        required=False,
        label="Período 2 - Data final",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )

    # Seletores intuitivos de mês/ano para comparação
    mes_comparacao1 = forms.ChoiceField(
        choices=MESES_CHOICES,
        required=False,
        label="Período 1 - Mês",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    ano_comparacao1 = forms.ChoiceField(
        choices=get_anos_choices,
        required=False,
        label="Período 1 - Ano",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    mes_comparacao2 = forms.ChoiceField(
        choices=MESES_CHOICES,
        required=False,
        label="Período 2 - Mês",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    ano_comparacao2 = forms.ChoiceField(
        choices=get_anos_choices,
        required=False,
        label="Período 2 - Ano",
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    # Campo de subtítulo personalizado para comparação entre períodos
    subtitulo_personalizado_comparacao = forms.CharField(
        max_length=100,
        required=False,
        label="Subtítulo personalizado",
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Ex: Comparação trimestral 2023 vs 2024'})
    )

    cliente = forms.CharField(max_length=100, required=False, label="Cliente")
    limite = forms.IntegerField(min_value=1, max_value=100, initial=10, required=False,
                               label="Limite de resultados")

    def clean(self):
        cleaned_data = super().clean()
        periodo = cleaned_data.get('periodo')
        data_inicial = cleaned_data.get('data_inicial')
        data_final = cleaned_data.get('data_final')
        meses_selecionados = cleaned_data.get('meses_selecionados')

        # Campos de comparação
        data_inicial_comp1 = cleaned_data.get('data_inicial_comparacao1')
        data_final_comp1 = cleaned_data.get('data_final_comparacao1')
        data_inicial_comp2 = cleaned_data.get('data_inicial_comparacao2')
        data_final_comp2 = cleaned_data.get('data_final_comparacao2')

        if periodo == 'personalizado':
            if not data_inicial:
                self.add_error('data_inicial', 'Data inicial é obrigatória para período personalizado.')
            if not data_final:
                self.add_error('data_final', 'Data final é obrigatória para período personalizado.')
            if data_inicial and data_final and data_inicial > data_final:
                self.add_error('data_final', 'Data final deve ser maior ou igual à data inicial.')

        elif periodo == 'meses_especificos':
            if not meses_selecionados:
                self.add_error('meses_selecionados', 'Selecione pelo menos um mês para gerar o relatório.')

        elif periodo == 'comparacao':
            # Verificar se foram usados seletores de mês/ano
            mes_comp1 = cleaned_data.get('mes_comparacao1')
            ano_comp1 = cleaned_data.get('ano_comparacao1')
            mes_comp2 = cleaned_data.get('mes_comparacao2')
            ano_comp2 = cleaned_data.get('ano_comparacao2')

            # Se os seletores de mês/ano foram preenchidos, não exigir campos de data manual
            if mes_comp1 and ano_comp1 and mes_comp2 and ano_comp2:
                # Validação dos seletores de mês/ano está OK
                pass
            else:
                # Validações para comparação entre períodos usando campos de data manual
                if not data_inicial_comp1:
                    self.add_error('data_inicial_comparacao1', 'Data inicial do período 1 é obrigatória.')
                if not data_final_comp1:
                    self.add_error('data_final_comparacao1', 'Data final do período 1 é obrigatória.')
                if not data_inicial_comp2:
                    self.add_error('data_inicial_comparacao2', 'Data inicial do período 2 é obrigatória.')
                if not data_final_comp2:
                    self.add_error('data_final_comparacao2', 'Data final do período 2 é obrigatória.')

                # Validar se as datas estão em ordem correta
                if data_inicial_comp1 and data_final_comp1 and data_inicial_comp1 > data_final_comp1:
                    self.add_error('data_final_comparacao1', 'Data final deve ser maior ou igual à data inicial.')
                if data_inicial_comp2 and data_final_comp2 and data_inicial_comp2 > data_final_comp2:
                    self.add_error('data_final_comparacao2', 'Data final deve ser maior ou igual à data inicial.')

        return cleaned_data


class RelatorioVendasPorMolaForm(forms.Form):
    """Formulário para relatório de vendas por mola específica"""

    # Lista de meses para seleção
    MESES_CHOICES = [
        ('1', 'Janeiro'),
        ('2', 'Fevereiro'),
        ('3', 'Março'),
        ('4', 'Abril'),
        ('5', 'Maio'),
        ('6', 'Junho'),
        ('7', 'Julho'),
        ('8', 'Agosto'),
        ('9', 'Setembro'),
        ('10', 'Outubro'),
        ('11', 'Novembro'),
        ('12', 'Dezembro'),
    ]

    mola = forms.ModelChoiceField(
        queryset=Mola.objects.all().order_by('cliente', 'codigo'),
        empty_label="Selecione uma mola",
        widget=forms.Select(attrs={'class': 'form-control'}),
        label="Mola"
    )

    # Campos para período personalizado
    usar_periodo_personalizado = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        label="Filtrar por período específico"
    )

    mes_inicial = forms.ChoiceField(
        choices=MESES_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label="Mês Inicial"
    )

    ano_inicial = forms.IntegerField(
        required=False,
        min_value=2020,
        max_value=2030,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Ex: 2024'}),
        label="Ano Inicial"
    )

    mes_final = forms.ChoiceField(
        choices=MESES_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label="Mês Final"
    )

    ano_final = forms.IntegerField(
        required=False,
        min_value=2020,
        max_value=2030,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Ex: 2024'}),
        label="Ano Final"
    )

    formato = forms.ChoiceField(
        choices=[
            ('tela', 'Visualizar na tela'),
            ('pdf', 'Exportar PDF'),
            ('csv', 'Exportar CSV')
        ],
        initial='tela',
        widget=forms.Select(attrs={'class': 'form-control'}),
        label="Formato"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Personalizar a exibição das molas no dropdown
        self.fields['mola'].queryset = Mola.objects.all().order_by('cliente', 'codigo')
        self.fields['mola'].label_from_instance = lambda obj: f"{obj.codigo} - {obj.cliente}"


class RelatorioMolasNaoVendidasForm(forms.Form):
    """Formulário para relatório de molas não vendidas"""

    periodo_dias = forms.IntegerField(
        initial=30,
        min_value=1,
        max_value=365,
        widget=forms.NumberInput(attrs={'class': 'form-control'}),
        label="Período (dias)",
        help_text="Número de dias para considerar como 'não vendidas'"
    )

    formato = forms.ChoiceField(
        choices=[
            ('tela', 'Visualizar na tela'),
            ('pdf', 'Exportar PDF'),
            ('csv', 'Exportar CSV')
        ],
        initial='tela',
        widget=forms.Select(attrs={'class': 'form-control'}),
        label="Formato"
    )


class PrevisaoDemandaForm(forms.Form):
    PERIODO_CHOICES = [
        ('S', 'Semanal'),
        ('M', 'Mensal'),
        ('T', 'Trimestral'),
    ]

    # Métodos básicos + avançados
    METODO_CHOICES = [
        ('auto', 'Automático (Recomendado)'),
        ('MM', 'Média Móvel'),
        ('SE', 'Suavização Exponencial / ARIMA'),
        ('RL', 'Regressão Linear / Prophet'),
        ('AI', 'Ensemble (Combinação de Modelos)'),
    ]

    mola = forms.ModelChoiceField(
        queryset=Mola.objects.all().order_by('codigo'),
        label="Mola",
        required=True,
        empty_label="Selecione uma mola"
    )

    periodo = forms.ChoiceField(
        choices=PERIODO_CHOICES,
        label="Período",
        initial='M',
        help_text="Período para o qual a previsão será gerada"
    )

    metodo = forms.ChoiceField(
        choices=METODO_CHOICES,
        label="Método de Previsão",
        initial='auto',
        help_text="Método utilizado para gerar a previsão"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Verificar se os métodos avançados estão disponíveis
        try:
            from .forecasting import STATSMODELS_INSTALLED, PROPHET_INSTALLED

            # Se os métodos avançados não estiverem disponíveis, ajustar as opções
            if not STATSMODELS_INSTALLED and not PROPHET_INSTALLED:
                self.fields['metodo'].choices = [
                    ('auto', 'Automático (Básico)'),
                    ('MM', 'Média Móvel'),
                ]
            elif not STATSMODELS_INSTALLED:
                self.fields['metodo'].choices = [
                    ('auto', 'Automático (Recomendado)'),
                    ('MM', 'Média Móvel'),
                    ('RL', 'Regressão Linear / Prophet'),
                ]
            elif not PROPHET_INSTALLED:
                self.fields['metodo'].choices = [
                    ('auto', 'Automático (Recomendado)'),
                    ('MM', 'Média Móvel'),
                    ('SE', 'Suavização Exponencial / ARIMA'),
                ]
        except ImportError:
            # Se o módulo de previsão avançada não estiver disponível, usar apenas métodos básicos
            self.fields['metodo'].choices = [
                ('MM', 'Média Móvel'),
                ('SE', 'Suavização Exponencial'),
                ('RL', 'Regressão Linear'),
            ]


class RelatorioEstoqueForm(forms.Form):
    ORDENACAO_CHOICES = [
        ('codigo', 'Código'),
        ('cliente', 'Cliente'),
        ('quantidade_estoque', 'Quantidade em Estoque'),
        ('estoque_minimo', 'Estoque Mínimo'),
    ]

    cliente = forms.CharField(max_length=100, required=False, label="Cliente")
    codigo = forms.CharField(max_length=100, required=False, label="Código da Mola")
    estoque_baixo = forms.BooleanField(required=False, label="Apenas Estoque Baixo", initial=False)
    ordenacao = forms.ChoiceField(choices=ORDENACAO_CHOICES, required=False, label="Ordenar por", initial='codigo')





class PlanejamentoProducaoForm(forms.ModelForm):
    ordem_fabricacao_numero = forms.CharField(
        label='Ordem de Fabricação Nº',
        max_length=50,
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )

    mola = forms.ModelChoiceField(
        label='Mola',
        queryset=Mola.objects.all(),
        required=True,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    material = forms.ModelChoiceField(
        label='Material',
        queryset=Material.objects.all(),  # Mostrar todos os materiais, não apenas os ativos
        required=False,  # Não obrigatório na criação de molas
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    quantidade_produzir = forms.IntegerField(
        label='Quantidade a Produzir',
        min_value=1,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )

    responsavel = forms.CharField(
        label='Responsável',
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )

    class Meta:
        model = PlanejamentoProducao
        fields = ['ordem_fabricacao_numero', 'data_inicio', 'mola', 'material', 'quantidade_produzir', 'responsavel', 'status', 'observacoes']
        widgets = {
            'data_inicio': forms.DateInput(attrs={'type': 'date', 'class': 'form-control', 'label': 'Data de Emissão'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'observacoes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }
        labels = {
            'data_inicio': 'Data de Emissão',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Renomear o campo data_inicio para Data de Emissão
        self.fields['data_inicio'].label = 'Data de Emissão'

        # Inicializar com a data atual
        if not self.instance.pk:
            self.fields['data_inicio'].initial = timezone.now().date()

        # Ordenar as molas por código
        if 'mola' in self.fields:
            self.fields['mola'].queryset = Mola.objects.all().order_by('codigo')

        # Ordenar os materiais por nome e diâmetro numérico
        if 'material' in self.fields:
            # Usar QuerySet diretamente ao invés de lista para evitar erro 'list' object has no attribute 'all'
            self.fields['material'].queryset = Material.objects.all().order_by('nome', 'diametro')

        # Não filtrar automaticamente o material quando uma mola é selecionada
        # O JavaScript cuidará de mostrar os materiais do mesmo tipo padrão

    def clean(self):
        cleaned_data = super().clean()
        mola = cleaned_data.get('mola')
        material = cleaned_data.get('material')
        quantidade = cleaned_data.get('quantidade_produzir')

        # Se a mola foi selecionada mas o material não, não exigir material
        # O material será selecionado na ordem de fabricação
        if mola and not material:
            # Não exigir material neste momento
            pass

        # Verificar se o material selecionado é o mesmo da mola ou do mesmo material padrão
        elif mola and material:
            if mola.material_padrao and material.material_padrao:
                if mola.material_padrao != material.material_padrao:
                    self.add_error('material', 'O material selecionado não corresponde ao tipo padrão da mola.')

        # Verificar se há material suficiente para produzir a quantidade solicitada
        if mola and material and quantidade:
            if mola.peso_unitario:
                # Converter de gramas para quilogramas (dividir por 1000)
                peso_por_mola = mola.peso_unitario / Decimal('1000')
            else:
                # Usar valor padrão se não houver peso unitário informado (1g = 0.001kg)
                peso_por_mola = Decimal('0.001')

            material_necessario = Decimal(str(quantidade)) * peso_por_mola

            if material.quantidade_estoque < material_necessario:
                self.add_error('quantidade_produzir',
                              f'Material insuficiente. Necessário: {material_necessario:.2f} kg, Disponível: {material.quantidade_estoque:.2f} kg')

        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)

        # Configurar campos adicionais
        instance.nome = f"O.F. {self.cleaned_data['ordem_fabricacao_numero']}"
        # A data_inicio será atualizada quando a produção for iniciada
        # Mantemos a data_fim_prevista igual à data_inicio para compatibilidade
        instance.data_fim_prevista = instance.data_inicio
        instance.prioridade = 2  # Normal

        # Salvar o material selecionado
        if 'material' in self.cleaned_data and self.cleaned_data['material']:
            instance.material = self.cleaned_data['material']

        if commit:
            instance.save()

            # Criar o item de planejamento para a mola
            ItemPlanejamento.objects.create(
                planejamento=instance,
                mola=self.cleaned_data['mola'],
                quantidade=self.cleaned_data['quantidade_produzir'],
                status=instance.status,
                prioridade=instance.prioridade,
                origem=f"Ordem de Fabricação #{self.cleaned_data['ordem_fabricacao_numero']}"
            )

        return instance


class ItemPlanejamentoForm(forms.ModelForm):
    class Meta:
        model = ItemPlanejamento
        fields = ['planejamento', 'mola', 'quantidade', 'status', 'prioridade', 'origem', 'observacoes']
        widgets = {
            'observacoes': forms.Textarea(attrs={'rows': 3}),
        }

    def clean_quantidade(self):
        quantidade = self.cleaned_data.get('quantidade')
        if quantidade <= 0:
            raise ValidationError('A quantidade deve ser maior que zero.')
        return quantidade


class PlanejamentoAutomaticoForm(forms.Form):
    data_inicio = forms.DateField(
        label='Data de Início',
        widget=forms.DateInput(attrs={'type': 'date'}),
        initial=timezone.now().date
    )

    dias_duracao = forms.IntegerField(
        label='Duração (dias)',
        min_value=1,
        max_value=90,
        initial=7,
        help_text='Duração da ordem de fabricação em dias'
    )

    pedidos = forms.ModelMultipleChoiceField(
        label='Pedidos a Incluir',
        queryset=PedidoVenda.objects.filter(status='P'),
        required=False,
        widget=forms.CheckboxSelectMultiple,
        help_text='Selecione os pedidos pendentes que deseja incluir na ordem de fabricação'
    )

    previsoes = forms.ModelMultipleChoiceField(
        label='Previsões a Incluir',
        queryset=PrevisaoDemanda.objects.filter(data_fim__gte=timezone.now().date()),
        required=False,
        widget=forms.CheckboxSelectMultiple,
        help_text='Selecione as previsões de demanda que deseja incluir na ordem de fabricação'
    )

    incluir_todos_pedidos = forms.BooleanField(
        label='Incluir todos os pedidos pendentes',
        required=False,
        initial=True
    )

    incluir_todas_previsoes = forms.BooleanField(
        label='Incluir todas as previsões ativas',
        required=False,
        initial=False
    )

    def clean(self):
        cleaned_data = super().clean()
        data_inicio = cleaned_data.get('data_inicio')

        if data_inicio and data_inicio < timezone.now().date():
            self.add_error('data_inicio', 'A data de início deve ser igual ou posterior à data atual.')

        incluir_todos_pedidos = cleaned_data.get('incluir_todos_pedidos')
        pedidos = cleaned_data.get('pedidos')

        incluir_todas_previsoes = cleaned_data.get('incluir_todas_previsoes')
        previsoes = cleaned_data.get('previsoes')

        if not incluir_todos_pedidos and not pedidos and not incluir_todas_previsoes and not previsoes:
            self.add_error(None, 'Selecione pelo menos um pedido ou previsão, ou marque para incluir todos.')

        return cleaned_data


class RegistroProducaoForm(forms.Form):
    quantidade = forms.IntegerField(
        label='Quantidade Produzida',
        min_value=1,
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )


class FinalizarOrdemFabricacaoForm(forms.Form):
    quantidade_produzida = forms.IntegerField(
        label='Quantidade Produzida',
        min_value=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )

    def clean(self):
        cleaned_data = super().clean()
        quantidade_vendida = cleaned_data.get('quantidade_vendida')
        quantidade_estoque = cleaned_data.get('quantidade_estoque')

        if quantidade_vendida is not None and quantidade_estoque is not None:
            total = quantidade_vendida + quantidade_estoque
            if total <= 0:
                raise ValidationError('A soma das quantidades deve ser maior que zero.')

        return cleaned_data


