{% load static %}
<!DOCTYPE html>
<html lang="pt-br" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}<PERSON><PERSON> Rios - Controle de Estoque{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/custom.css' %}">
    <link rel="stylesheet" href="{% static 'css/status_badges.css' %}">
    <link rel="stylesheet" href="{% static 'css/badge_override.css' %}">
    <style>
        :root {
            --dark-bg: #121212;
            --dark-surface: #1e1e1e;
            --dark-primary: #bb86fc;
            --dark-secondary: #03dac6;
            --dark-error: #cf6679;
            --dark-text-primary: rgba(255, 255, 255, 0.87);
            --dark-text-secondary: rgba(255, 255, 255, 0.6);

            /* Cores para os badges de status */
            --status-pendente: #ffc107;
            --status-em-andamento: #0d6efd;
            --status-concluido: #198754;
            --status-atrasado: #fd7e14;
            --status-cancelado: #dc3545;
        }

        /* Estilos para os badges de status */
        .badge {
            display: inline-block;
            padding: 0.25em 0.4em;
            font-size: 75%;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }

        .badge.pendente {
            background-color: var(--status-pendente) !important;
            color: #000 !important;
        }

        .badge.em-andamento {
            background-color: var(--status-em-andamento) !important;
            color: #fff !important;
        }

        .badge.concluido {
            background-color: var(--status-concluido) !important;
            color: #fff !important;
        }

        .badge.atrasado {
            background-color: var(--status-atrasado) !important;
            color: #000 !important;
        }

        .badge.cancelado {
            background-color: var(--status-cancelado) !important;
            color: #fff !important;
        }

        body {
            background-color: var(--dark-bg);
            color: var(--dark-text-primary);
        }

        /* Sidebar principal */
        .sidebar {
            position: fixed;
            top: 56px;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background-color: var(--dark-surface);
            overflow-y: auto;
            max-height: calc(100vh - 56px);
        }

        @media (max-width: 767.98px) {
            .sidebar {
                position: static;
                max-height: none;
                padding-top: 15px;
            }
        }

        .sidebar .position-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 56px);
            padding-top: 1rem;
            overflow-x: hidden;
            overflow-y: auto;
        }

        /* Links da sidebar */
        .sidebar .nav-link {
            font-weight: 500;
            color: var(--dark-text-secondary);
            padding: 0.75rem 1rem;
            border-radius: 0;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--dark-text-primary);
        }

        .sidebar .nav-link.active {
            background-color: var(--dark-primary);
            color: #000;
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        /* Dropdowns na sidebar */
        .sidebar .dropdown-menu {
            position: static !important;
            float: none;
            width: auto;
            margin-top: 0;
            background-color: rgba(0, 0, 0, 0.15);
            border: none;
            border-radius: 0;
            box-shadow: none;
            padding: 0;
            transform: none !important;
        }

        .sidebar .dropdown-item {
            padding: 0.5rem 1rem 0.5rem 3rem;
            color: var(--dark-text-secondary);
            font-size: 0.9rem;
        }

        .sidebar .dropdown-item:hover {
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--dark-text-primary);
        }

        .sidebar .dropdown-item.active {
            background-color: var(--dark-primary);
            color: #000;
        }

        /* Ajuste para o conteúdo principal */
        @media (min-width: 768px) {
            .ms-sm-auto {
                margin-left: 16.666667% !important;
            }
        }

        @media (min-width: 992px) {
            .ms-sm-auto {
                margin-left: 16.666667% !important;
            }
        }

        .content {
            padding: 20px;
        }

        .alert-container {
            position: fixed;
            top: 70px;
            right: 20px;
            z-index: 1050;
            max-width: 350px;
        }

        .footer {
            background-color: var(--dark-surface);
            padding: 15px 0;
            text-align: center;
            margin-top: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .card {
            background-color: var(--dark-surface);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .card-header {
            background-color: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .table {
            color: var(--dark-text-primary);
        }

        .table-striped > tbody > tr:nth-of-type(odd) {
            background-color: rgba(255, 255, 255, 0.03);
        }

        .table-bordered {
            border-color: rgba(255, 255, 255, 0.1);
        }

        .form-control, .form-select {
            background-color: var(--dark-surface);
            border-color: rgba(255, 255, 255, 0.2);
            color: var(--dark-text-primary);
        }

        .form-control:focus, .form-select:focus {
            background-color: var(--dark-surface);
            color: var(--dark-text-primary);
            border-color: var(--dark-primary);
            box-shadow: 0 0 0 0.25rem rgba(187, 134, 252, 0.25);
        }

        .btn-primary {
            background-color: var(--dark-primary);
            border-color: var(--dark-primary);
            color: #000;
        }

        .btn-primary:hover {
            background-color: #a370db;
            border-color: #a370db;
            color: #000;
        }

        .btn-success {
            background-color: var(--dark-secondary);
            border-color: var(--dark-secondary);
            color: #000;
        }

        .btn-success:hover {
            background-color: #02b8a7;
            border-color: #02b8a7;
            color: #000;
        }

        .dropdown-menu {
            background-color: var(--dark-surface);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .dropdown-item {
            color: var(--dark-text-secondary);
        }

        .dropdown-item:hover {
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--dark-text-primary);
        }

        .navbar {
            background-color: var(--dark-primary) !important;
        }

        .navbar-brand, .navbar-nav .nav-link {
            color: #000 !important;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background-color: var(--dark-primary);">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'dashboard' %}">
                <i class="fas fa-box-open"></i> Molas Rios - Controle de Estoque
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'alertas-estoque' %}">
                            <i class="fas fa-bell"></i> Alertas
                            <span class="badge bg-danger">{{ alertas_count }}</span>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> Usuário
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#">Perfil</a></li>
                            <li><a class="dropdown-item" href="#">Configurações</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#">Sair</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle {% if request.path == '/' or '/dashboard/' in request.path %}active{% endif %}" href="#" id="dashboardDropdown" role="button">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item {% if request.path == '/' %}active{% endif %}" href="{% url 'dashboard' %}">
                                        Dashboard Básico
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item {% if '/dashboard/kpi/' in request.path %}active{% endif %}" href="{% url 'dashboard-kpi' %}">
                                        KPIs e Análises
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if '/molas/' in request.path %}active{% endif %}" href="{% url 'mola-list' %}">
                                <i class="fas fa-cog"></i> Molas
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if '/materiais/' in request.path %}active{% endif %}" href="{% url 'material-list' %}">
                                <i class="fas fa-cubes"></i> Materiais
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if '/movimentacoes/estoque/' in request.path %}active{% endif %}" href="{% url 'movimentacao-estoque-list' %}">
                                <i class="fas fa-exchange-alt"></i> Movimentações de Molas
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if '/movimentacoes/material/' in request.path %}active{% endif %}" href="{% url 'movimentacao-material-list' %}">
                                <i class="fas fa-exchange-alt"></i> Movimentações de Materiais
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if '/pedidos/' in request.path %}active{% endif %}" href="{% url 'pedido-venda-list' %}">
                                <i class="fas fa-file-invoice"></i> Pedidos de Venda
                            </a>
                        </li>

                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle {% if '/relatorios/' in request.path %}active{% endif %}" href="#" id="relatoriosDropdown" role="button">
                                <i class="fas fa-chart-bar"></i> Relatórios
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item {% if '/relatorios/molas-mais-vendidas/' in request.path %}active{% endif %}" href="{% url 'relatorio-molas-mais-vendidas' %}">
                                        Molas Mais Vendidas
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item {% if '/relatorios/vendas-por-mola/' in request.path %}active{% endif %}" href="{% url 'relatorio-vendas-por-mola' %}">
                                        Vendas por Mola
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item {% if '/relatorios/estoque/' in request.path %}active{% endif %}" href="{% url 'relatorio-estoque' %}">
                                        Relatório de Estoque
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if '/previsao-demanda/' in request.path %}active{% endif %}" href="{% url 'previsao-demanda-list' %}">
                                <i class="fas fa-chart-line"></i> Previsão de Demanda
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if '/analise-obsolescencia/' in request.path %}active{% endif %}" href="{% url 'analise-obsolescencia-list' %}">
                                <i class="fas fa-hourglass-end"></i> Análise de Obsolescência
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle {% if '/planejamento/' in request.path %}active{% endif %}" href="#" id="planejamentoDropdown" role="button">
                                <i class="fas fa-tasks"></i> Ordens de Fabricação
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item {% if request.path == '/planejamento/' %}active{% endif %}" href="{% url 'planejamento-list' %}">
                                        Listar Ordens de Fabricação
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item {% if '/planejamento/novo/' in request.path %}active{% endif %}" href="{% url 'planejamento-create' %}">
                                        Nova Ordem de Fabricação
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item {% if '/planejamento/automatico/' in request.path %}active{% endif %}" href="{% url 'planejamento-automatico' %}">
                                        Ordens Automáticas
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item {% if '/dashboard/producao/' in request.path %}active{% endif %}" href="{% url 'dashboard-producao' %}">
                                        Dashboard de Produção
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
                <!-- Alerts -->
                <div class="alert-container">
                    {% if messages %}
                        {% for message in messages %}
                            {% if message.tags != 'success' %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                </div>

                <!-- Page content -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer mt-auto py-3">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <span class="text-muted">© 2025 Molas Rios. Todos os direitos reservados.</span>
                </div>
                {% if system_resources %}
                <div class="col-md-6 text-end">
                    <span class="text-muted">
                        <i class="fas fa-memory" data-bs-toggle="tooltip" title="Uso de memória"></i> {{ system_resources.memory_usage_mb }} MB |
                        <i class="fas fa-microchip" data-bs-toggle="tooltip" title="Uso de CPU"></i> {{ system_resources.cpu_percent }}% |
                        <i class="fas fa-clock" data-bs-toggle="tooltip" title="Tempo de execução"></i> {{ system_resources.uptime_hours }} h
                    </span>
                </div>
                {% endif %}
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    <script src="{% static 'js/utils.js' %}"></script>

    {% block extra_js %}{% endblock %}

    <script>
        // Auto-hide alerts after 5 seconds
        $(document).ready(function() {
            // Forçar cores dos badges
            $('.badge.bg-primary').css({
                'background-color': '#ffc107 !important',
                'color': '#000 !important'
            });

            $('.badge.bg-info').css({
                'background-color': '#0d6efd !important',
                'color': '#fff !important'
            });

            // Inicializar tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl, {
                    delay: { show: 500, hide: 100 },  // Atraso para mostrar/esconder
                    animation: false,                 // Sem animação
                    trigger: 'hover focus',           // Mostrar apenas no hover ou foco
                    container: 'body'                 // Renderizar no body para evitar problemas de posicionamento
                });
            });

            // Configurar os dropdowns da sidebar
            $('.sidebar .nav-item.dropdown').each(function() {
                var $dropdown = $(this);
                var $link = $dropdown.find('.nav-link.dropdown-toggle');
                var $menu = $dropdown.find('.dropdown-menu');

                // Verificar se o dropdown deve estar aberto (item ativo)
                if ($link.hasClass('active') || $menu.find('.dropdown-item.active').length > 0) {
                    $menu.addClass('show');
                }

                // Alternar o menu ao clicar no link
                $link.on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Fechar outros menus abertos
                    $('.sidebar .dropdown-menu').not($menu).removeClass('show');

                    // Alternar o menu atual
                    $menu.toggleClass('show');
                });
            });

            // Evitar que os cliques nos itens do dropdown fechem o menu
            $('.sidebar .dropdown-menu').on('click', function(e) {
                e.stopPropagation();
            });

            // Fechar os dropdowns ao clicar fora
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.sidebar .nav-item.dropdown').length) {
                    $('.sidebar .dropdown-menu').removeClass('show');
                }
            });

            // Remover pop-ups de sucesso (cor verde) conforme solicitado pelo usuário
            // Os demais alertas não desaparecem automaticamente e precisam ser fechados manualmente
        });
    </script>
</body>
</html>
